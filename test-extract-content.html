<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Extract Content</title>
</head>
<body>
    <h1>Test Page for Content Extraction</h1>
    
    <!-- This is a very short content that should trigger Jina AI fallback -->
    <div id="content">
        <p>Short content.</p>
    </div>
    
    <script>
        // Mock the extractWebContent function for testing
        async function testExtractWebContent() {
            // Simulate a short article that would trigger Jina AI fallback
            const mockArticle = {
                textContent: "Short content.",
                title: "Test Page",
                content: "Short content.",
                length: 14
            };
            
            console.log('Original article length:', mockArticle.textContent.length);
            
            if (mockArticle && mockArticle.textContent && mockArticle.textContent.length < 100) {
                try {
                    console.log('Content too short, trying Jina AI...');
                    const currentUrl = window.location.href;
                    const jinaResponse = await fetch(`https://r.jina.ai/${currentUrl}`);
                    
                    if (jinaResponse.ok) {
                        const jinaContent = await jinaResponse.text();
                        console.log('Jina AI response:', jinaContent);
                        
                        // Parse Jina AI response
                        const lines = jinaContent.split('\n');
                        let title = '';
                        let content = '';
                        let isMarkdownContent = false;
                        
                        for (const line of lines) {
                            if (line.startsWith('Title: ')) {
                                title = line.substring(7).trim();
                            } else if (line.startsWith('Markdown Content:')) {
                                isMarkdownContent = true;
                            } else if (isMarkdownContent && line.trim()) {
                                content += line + '\n';
                            }
                        }
                        
                        console.log('Parsed title:', title);
                        console.log('Parsed content length:', content.length);
                        
                        if (content.length > mockArticle.textContent.length) {
                            console.log('Using Jina AI content');
                            return {
                                title: title || mockArticle.title,
                                textContent: content.trim(),
                                content: content.trim(),
                                excerpt: content.substring(0, 200).trim() + (content.length > 200 ? '...' : ''),
                                length: content.length
                            };
                        }
                    }
                } catch (error) {
                    console.warn('Failed to fetch content from Jina AI:', error);
                }
            }
            
            console.log('Using original content');
            return mockArticle;
        }
        
        // Test the function
        testExtractWebContent().then(result => {
            console.log('Final result:', result);
            
            // Display results on page
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h2>Test Results:</h2>
                <p><strong>Title:</strong> ${result.title}</p>
                <p><strong>Content Length:</strong> ${result.textContent?.length || 0} characters</p>
                <p><strong>Content Preview:</strong> ${result.textContent?.substring(0, 200) || 'No content'}...</p>
            `;
            document.body.appendChild(resultDiv);
        });
    </script>
</body>
</html>
